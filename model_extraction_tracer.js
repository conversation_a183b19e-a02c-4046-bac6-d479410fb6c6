// Adobe Scan 模型提取和分析脚本
// 专门用于定位实时边缘检测模型并尝试导出

console.log("[+] Adobe Scan 模型提取脚本已启动");

var hooksInstalled = false;
var modelBuffers = [];
var nativeModelData = [];

// 主要的模型提取函数
function extractModels() {
    if (hooksInstalled) {
        console.log("[!] 钩子已经安装，无需重复操作");
        return;
    }

    console.log("[+] 开始模型提取和分析...");

    // 安装模型缓冲区钩子
    installModelBufferHooks();
    
    // 安装 Native 层内存钩子
    installNativeMemoryHooks();
    
    // 分析 libMagicClean.so 中的模型数据
    analyzeNativeModelData();
    
    // 尝试导出已知模型文件
    exportKnownModels();
    
    hooksInstalled = true;
}

// 安装模型缓冲区钩子
function installModelBufferHooks() {
    console.log("[+] 安装模型缓冲区钩子...");

    Java.perform(function() {
        try {
            // 1. 钩住 H5.p.b() - 主要的模型加载方法
            var H5p = Java.use("H5.p");
            if (H5p) {
                console.log("[+] 成功钩住 H5.p");
                
                H5p.b.implementation = function(context, modelPath) {
                    console.log("╔══════════════════════════════════════════════════════════════╗");
                    console.log("║                    模型文件加载拦截                          ║");
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 模型路径: " + modelPath);
                    console.log("║ 上下文: " + context);
                    console.log("╚══════════════════════════════════════════════════════════════╝");
                    
                    var result = this.b(context, modelPath);
                    
                    if (result) {
                        console.log("╔══════════════════════════════════════════════════════════════╗");
                        console.log("║                    模型缓冲区信息                            ║");
                        console.log("╠══════════════════════════════════════════════════════════════╣");
                        console.log("║ 模型路径: " + modelPath);
                        console.log("║ 缓冲区大小: " + result.capacity() + " bytes");
                        console.log("║ 缓冲区位置: " + result.position());
                        console.log("║ 缓冲区限制: " + result.limit());
                        console.log("║ 缓冲区类型: " + result.getClass().getName());
                        console.log("║ 是否直接缓冲区: " + result.isDirect());
                        console.log("║ 是否只读: " + result.isReadOnly());
                        console.log("╚══════════════════════════════════════════════════════════════╝");
                        
                        // 保存模型缓冲区信息
                        var modelInfo = {
                            path: modelPath,
                            buffer: result,
                            size: result.capacity(),
                            timestamp: new Date().getTime()
                        };
                        modelBuffers.push(modelInfo);
                        
                        // 尝试导出模型数据
                        exportModelBuffer(modelInfo);
                        
                        // 分析模型类型
                        analyzeModelType(modelPath, result);
                    }
                    
                    return result;
                };
            }
        } catch (e) {
            console.log("[-] H5.p 钩子安装失败: " + e);
        }

        try {
            // 2. 钩住 TensorFlow Lite 模型创建
            var NativeInterpreterWrapper = Java.use("org.tensorflow.lite.NativeInterpreterWrapper");
            if (NativeInterpreterWrapper) {
                console.log("[+] 成功钩住 NativeInterpreterWrapper");
                
                NativeInterpreterWrapper.$init.overload('java.nio.MappedByteBuffer', 'org.tensorflow.lite.e$a').implementation = function(buffer, options) {
                    console.log("╔══════════════════════════════════════════════════════════════╗");
                    console.log("║                TensorFlow Lite 解释器创建                    ║");
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 缓冲区大小: " + buffer.capacity() + " bytes");
                    console.log("║ 缓冲区位置: " + buffer.position());
                    console.log("║ 缓冲区限制: " + buffer.limit());
                    console.log("║ 选项: " + options);
                    console.log("╚══════════════════════════════════════════════════════════════╝");
                    
                    // 尝试读取模型头部信息
                    analyzeModelHeader(buffer);
                    
                    return this.$init(buffer, options);
                };
            }
        } catch (e) {
            console.log("[-] NativeInterpreterWrapper 钩子安装失败: " + e);
        }
    });
}

// 安装 Native 层内存钩子
function installNativeMemoryHooks() {
    console.log("[+] 安装 Native 层内存钩子...");

    // 检查 libMagicClean.so
    var magicCleanSo = Process.findModuleByName("libMagicClean.so");
    if (!magicCleanSo) {
        magicCleanSo = Process.findModuleByName("libmagicclean.so");
    }

    if (magicCleanSo) {
        console.log("[+] 找到 MagicClean 模块: " + magicCleanSo.base);
        
        try {
            // 钩住可能的模型加载函数
            var exports = magicCleanSo.enumerateExports();
            exports.forEach(function(exp) {
                if (exp.name.toLowerCase().includes("model") || 
                    exp.name.toLowerCase().includes("load") ||
                    exp.name.toLowerCase().includes("init")) {
                    console.log("[+] 发现可能的模型相关函数: " + exp.name + " @ " + exp.address);
                    
                    try {
                        Interceptor.attach(exp.address, {
                            onEnter: function(args) {
                                console.log("[NATIVE] 调用函数: " + exp.name);
                                console.log("  参数数量: " + args.length);
                                for (var i = 0; i < Math.min(4, args.length); i++) {
                                    console.log("  参数[" + i + "]: " + args[i]);
                                }
                            },
                            onLeave: function(retval) {
                                console.log("[NATIVE] 函数返回: " + exp.name + " -> " + retval);
                            }
                        });
                    } catch (e) {
                        console.log("[-] 无法钩住函数 " + exp.name + ": " + e);
                    }
                }
            });
        } catch (e) {
            console.log("[-] Native 钩子安装失败: " + e);
        }
    }
}

// 分析 Native 层模型数据
function analyzeNativeModelData() {
    console.log("[+] 分析 Native 层模型数据...");

    var magicCleanSo = Process.findModuleByName("libMagicClean.so");
    if (!magicCleanSo) {
        magicCleanSo = Process.findModuleByName("libmagicclean.so");
    }

    if (magicCleanSo) {
        console.log("[+] 扫描 MagicClean 模块内存...");
        
        try {
            // 扫描可能的 TensorFlow Lite 模型签名
            var tfliteSignatures = [
                "TFL3",  // TensorFlow Lite v3
                "TFL2",  // TensorFlow Lite v2
                "TFL1",  // TensorFlow Lite v1
                "\x18\x00\x00\x00TFL3",  // 带长度前缀的签名
                "\x1c\x00\x00\x00TFL3"   // 另一种长度前缀
            ];
            
            var moduleSize = magicCleanSo.size;
            var scanSize = Math.min(moduleSize, 0x1000000); // 最多扫描 16MB
            
            console.log("╔══════════════════════════════════════════════════════════════╗");
            console.log("║                  扫描模块内存中的模型数据                    ║");
            console.log("╠══════════════════════════════════════════════════════════════╣");
            console.log("║ 模块基地址: " + magicCleanSo.base);
            console.log("║ 模块大小: " + moduleSize + " bytes");
            console.log("║ 扫描大小: " + scanSize + " bytes");
            console.log("╚══════════════════════════════════════════════════════════════╝");
            
            for (var i = 0; i < tfliteSignatures.length; i++) {
                var signature = tfliteSignatures[i];
                console.log("[+] 搜索签名: " + signature);
                
                try {
                    var results = Memory.scanSync(magicCleanSo.base, scanSize, signature);
                    if (results.length > 0) {
                        console.log("╔══════════════════════════════════════════════════════════════╗");
                        console.log("║                  发现 TensorFlow Lite 模型！                ║");
                        console.log("╠══════════════════════════════════════════════════════════════╣");
                        results.forEach(function(result, index) {
                            console.log("║ 位置[" + index + "]: " + result.address);
                            console.log("║ 偏移: 0x" + result.address.sub(magicCleanSo.base).toString(16));
                            
                            // 尝试读取模型头部信息
                            try {
                                var headerData = Memory.readByteArray(result.address, 64);
                                console.log("║ 头部数据: " + hexdump(headerData, {length: 64}));
                                
                                // 尝试确定模型大小
                                var modelSize = estimateModelSize(result.address);
                                if (modelSize > 0) {
                                    console.log("║ 估计模型大小: " + modelSize + " bytes");
                                    
                                    // 导出模型数据
                                    exportNativeModel(result.address, modelSize, "native_model_" + index + ".tflite");
                                }
                            } catch (e) {
                                console.log("║ 读取头部失败: " + e);
                            }
                        });
                        console.log("╚══════════════════════════════════════════════════════════════╝");
                    }
                } catch (e) {
                    console.log("[-] 扫描签名失败: " + e);
                }
            }
        } catch (e) {
            console.log("[-] 内存扫描失败: " + e);
        }
    }
}

// 导出模型缓冲区
function exportModelBuffer(modelInfo) {
    console.log("[+] 尝试导出模型缓冲区: " + modelInfo.path);
    
    try {
        Java.perform(function() {
            var buffer = modelInfo.buffer;
            var size = buffer.capacity();
            
            // 创建字节数组
            var byteArray = Java.array('byte', new Array(size));
            
            // 重置缓冲区位置
            buffer.rewind();
            
            // 读取数据到字节数组
            buffer.get(byteArray);
            
            // 转换为 JavaScript 数组
            var jsArray = [];
            for (var i = 0; i < size; i++) {
                jsArray[i] = byteArray[i] & 0xFF;
            }
            
            // 保存到文件
            var fileName = modelInfo.path.replace(/[\/\\]/g, '_') + '_exported.tflite';
            saveModelToFile(jsArray, fileName);
            
            console.log("╔══════════════════════════════════════════════════════════════╗");
            console.log("║                    模型导出成功                              ║");
            console.log("╠══════════════════════════════════════════════════════════════╣");
            console.log("║ 原始路径: " + modelInfo.path);
            console.log("║ 导出文件: " + fileName);
            console.log("║ 文件大小: " + size + " bytes");
            console.log("╚══════════════════════════════════════════════════════════════╝");
        });
    } catch (e) {
        console.log("[-] 导出模型缓冲区失败: " + e);
    }
}

// 导出 Native 模型
function exportNativeModel(address, size, fileName) {
    console.log("[+] 导出 Native 模型: " + fileName);
    
    try {
        var data = Memory.readByteArray(address, size);
        var jsArray = new Uint8Array(data);
        
        saveModelToFile(Array.from(jsArray), fileName);
        
        console.log("╔══════════════════════════════════════════════════════════════╗");
        console.log("║                Native 模型导出成功                           ║");
        console.log("╠══════════════════════════════════════════════════════════════╣");
        console.log("║ 内存地址: " + address);
        console.log("║ 导出文件: " + fileName);
        console.log("║ 文件大小: " + size + " bytes");
        console.log("╚══════════════════════════════════════════════════════════════╝");
    } catch (e) {
        console.log("[-] 导出 Native 模型失败: " + e);
    }
}

// 保存模型到文件
function saveModelToFile(dataArray, fileName) {
    try {
        // 使用 Frida 的文件操作
        var file = new File("/data/local/tmp/" + fileName, "wb");
        file.write(dataArray);
        file.close();
        
        console.log("[+] 模型已保存到: /data/local/tmp/" + fileName);
    } catch (e) {
        console.log("[-] 保存文件失败: " + e);
        
        // 备用方案：输出十六进制数据
        console.log("[+] 输出模型数据的十六进制表示 (前1024字节):");
        var hexStr = "";
        for (var i = 0; i < Math.min(1024, dataArray.length); i++) {
            hexStr += ("0" + dataArray[i].toString(16)).slice(-2);
            if (i % 16 === 15) hexStr += "\n";
            else if (i % 4 === 3) hexStr += " ";
        }
        console.log(hexStr);
    }
}

// 估计模型大小
function estimateModelSize(address) {
    try {
        // TensorFlow Lite 模型通常在头部包含大小信息
        var header = Memory.readByteArray(address, 32);
        var view = new DataView(header);
        
        // 尝试不同的大小字段位置
        var possibleSizes = [
            view.getUint32(4, true),   // 小端序
            view.getUint32(8, true),
            view.getUint32(12, true),
            view.getUint32(16, true)
        ];
        
        for (var i = 0; i < possibleSizes.length; i++) {
            var size = possibleSizes[i];
            if (size > 1024 && size < 50 * 1024 * 1024) { // 1KB - 50MB 范围
                return size;
            }
        }
        
        // 如果无法确定大小，返回默认值
        return 1024 * 1024; // 1MB
    } catch (e) {
        console.log("[-] 估计模型大小失败: " + e);
        return 0;
    }
}

// 分析模型类型
function analyzeModelType(modelPath, buffer) {
    console.log("[+] 分析模型类型: " + modelPath);
    
    // 根据文件名判断模型用途
    if (modelPath.includes("capture")) {
        console.log("  -> 这是文档捕获/边缘检测模型");
    } else if (modelPath.includes("page_turn")) {
        console.log("  -> 这是页面翻转检测模型");
    } else if (modelPath.includes("resize")) {
        console.log("  -> 这是图像缩放预处理模型");
    }
}

// 分析模型头部
function analyzeModelHeader(buffer) {
    try {
        Java.perform(function() {
            // 保存当前位置
            var originalPosition = buffer.position();
            
            // 重置到开始
            buffer.rewind();
            
            // 读取前64字节
            var headerBytes = Java.array('byte', new Array(64));
            buffer.get(headerBytes, 0, Math.min(64, buffer.remaining()));
            
            console.log("╔══════════════════════════════════════════════════════════════╗");
            console.log("║                    模型头部分析                              ║");
            console.log("╠══════════════════════════════════════════════════════════════╣");
            
            // 转换为十六进制字符串
            var hexStr = "";
            for (var i = 0; i < Math.min(32, headerBytes.length); i++) {
                hexStr += ("0" + (headerBytes[i] & 0xFF).toString(16)).slice(-2) + " ";
                if (i % 16 === 15) hexStr += "\n║ ";
            }
            console.log("║ 头部数据: " + hexStr);
            console.log("╚══════════════════════════════════════════════════════════════╝");
            
            // 恢复位置
            buffer.position(originalPosition);
        });
    } catch (e) {
        console.log("[-] 分析模型头部失败: " + e);
    }
}

// 导出已知模型文件
function exportKnownModels() {
    console.log("[+] 尝试导出已知模型文件...");
    
    var knownModels = [
        "models/capture_6_22_23.tflite",
        "models/page_turn_2im_002.tflite", 
        "models/resize_to_320.tflite"
    ];
    
    Java.perform(function() {
        try {
            var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
            var assetManager = context.getAssets();
            
            knownModels.forEach(function(modelPath) {
                try {
                    console.log("[+] 尝试导出: " + modelPath);
                    
                    var inputStream = assetManager.open(modelPath);
                    var available = inputStream.available();
                    
                    console.log("  模型大小: " + available + " bytes");
                    
                    var buffer = Java.array('byte', new Array(available));
                    inputStream.read(buffer);
                    inputStream.close();
                    
                    // 转换为 JavaScript 数组
                    var jsArray = [];
                    for (var i = 0; i < available; i++) {
                        jsArray[i] = buffer[i] & 0xFF;
                    }
                    
                    var fileName = modelPath.replace(/[\/\\]/g, '_') + '_direct_export.tflite';
                    saveModelToFile(jsArray, fileName);
                    
                } catch (e) {
                    console.log("[-] 导出 " + modelPath + " 失败: " + e);
                }
            });
        } catch (e) {
            console.log("[-] 无法访问 AssetManager: " + e);
        }
    });
}

// 导出的函数
global.extractModels = extractModels;
global.exportKnownModels = exportKnownModels;
global.analyzeNativeModelData = analyzeNativeModelData;

// 自动启动
console.log("[+] 模型提取脚本已准备就绪");
console.log("[+] 可用命令:");
console.log("    extractModels() - 开始模型提取");
console.log("    exportKnownModels() - 导出已知模型");
console.log("    analyzeNativeModelData() - 分析 Native 模型数据");

// 延迟启动
setTimeout(function() {
    console.log("[+] 自动启动模型提取...");
    extractModels();
}, 3000);
