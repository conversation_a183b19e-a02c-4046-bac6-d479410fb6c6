
setTimeout(() => {
    Java.perform(() => {
        traceEdgeDetection()
    });
}, 1000);

// 边缘检测跟踪脚本 - 增强版 v2.0

// 全局变量
var hooksInstalled = false;
var nativeHooksInstalled = false;

// 设置钩子的主函数 - 入口点
function traceEdgeDetection() {
    if (hooksInstalled) {
        console.log("[!] 钩子已经安装，无需重复操作");
        return;
    }

    console.log("[+] 开始跟踪边缘检测相关函数...");

    // 检查 libMagicClean.so 是否已加载
    var magicCleanSo = Process.findModuleByName("libMagicClean.so");
    if (!magicCleanSo) {
        magicCleanSo = Process.findModuleByName("libmagicclean.so");
    }

    if (magicCleanSo) {
        console.log("[+] 找到 MagicClean 模块，基地址: " + magicCleanSo.base);

        // 安装 Native 层钩子
        installNativeHooks(magicCleanSo);

        // 安装 Java 层钩子
        installJavaHooks();

        hooksInstalled = true;
    } else {
        console.log("[-] 未找到 MagicClean 模块，请确保应用已完全启动");
        // 延迟重试
        setTimeout(function() {
            hooksInstalled = false;
            traceEdgeDetection();
        }, 2000);
    }
}

// 安装 Native 层钩子
function installNativeHooks(magicCleanSo) {
    if (nativeHooksInstalled) {
        return;
    }

    console.log("[+] 安装 Native 层钩子...");

    try {
        // 列出导出函数
        var exports = magicCleanSo.enumerateExports();
        console.log("[+] 导出函数数量: " + exports.length);

        // 查找 JNI 函数
        exports.forEach(function(exp) {
            if (exp.name.indexOf("Java_") === 0) {
                console.log("[+] 找到 JNI 函数: " + exp.name + " @ " + exp.address);

                // 钩住 JNI 函数
                try {
                    Interceptor.attach(exp.address, {
                        onEnter: function(args) {
                            console.log("[JNI] 调用 " + exp.name);
                            console.log("    JNIEnv: " + args[0]);
                            console.log("    jobject: " + args[1]);

                            // 记录调用栈
                            console.log("    调用栈:");
                            Thread.backtrace(this.context, Backtracer.ACCURATE)
                                .map(DebugSymbol.fromAddress)
                                .slice(0, 10)
                                .forEach(function(symbol, index) {
                                    console.log("      " + index + ": " + symbol);
                                });
                        },
                        onLeave: function(retval) {
                            console.log("[JNI] " + exp.name + " 返回: " + retval);
                        }
                    });
                } catch (e) {
                    console.log("[-] 钩住 " + exp.name + " 失败: " + e);
                }
            }
        });

        // 尝试通过符号查找更多函数
        var symbols = magicCleanSo.enumerateSymbols();
        console.log("[+] 符号数量: " + symbols.length);

        // 查找边缘检测相关符号
        var edgeRelatedSymbols = [];
        symbols.forEach(function(sym) {
            var name = sym.name.toLowerCase();
            if (name.indexOf("edge") !== -1 ||
                name.indexOf("corner") !== -1 ||
                name.indexOf("detect") !== -1 ||
                name.indexOf("boundary") !== -1 ||
                name.indexOf("live") !== -1) {
                edgeRelatedSymbols.push(sym);
                console.log("[+] 边缘检测相关符号: " + sym.name + " @ " + sym.address);
            }
        });

        nativeHooksInstalled = true;
    } catch (e) {
        console.log("[-] 安装 Native 层钩子失败: " + e);
    }
}

// 安装 Java 层钩子
function installJavaHooks() {
    console.log("[+] 安装 Java 层钩子...");

    try {
        // 钩住 CameraCleanAndroidShim
        hookCameraCleanAndroidShim();

        // 钩住 CameraCleanLiveEdgeDetectionAndroidShim
        hookCameraCleanLiveEdgeDetectionAndroidShim();

        // 钩住 CameraCleanDocDetectionAndroidShim
        hookCameraCleanDocDetectionAndroidShim();

        // 钩住 UI 相关类
        hookUIClasses();

        // 钩住其他相关类
        hookOtherClasses();

    } catch (e) {
        console.log("[-] 安装 Java 层钩子失败: " + e);
    }
}
// 钩住 CameraCleanAndroidShim 类
function hookCameraCleanAndroidShim() {
    try {
        var CameraCleanAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanAndroidShim");

        // 跟踪 GetCorners 方法
        CameraCleanAndroidShim.GetCorners.implementation = function(input, output) {
            console.log("[JAVA] CameraCleanAndroidShim.GetCorners() 被调用");

            // 记录输入参数
            try {
                if (input && input.mInputImage) {
                    var bitmap = input.mInputImage.value;
                    console.log("    输入图像尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                }
                if (input && input.mDocSelectorType) {
                    console.log("    文档选择器类型: " + input.mDocSelectorType.value);
                }
            } catch (e) {
                console.log("    获取输入参数失败: " + e);
            }

            var result = this.GetCorners(input, output);

            // 记录输出结果 - 增强版
            try {
                if (output) {
                    console.log("    检测结果: " + result);
                    if (output.mCCornersInfoVec && output.mCCornersInfoVec.value) {
                        var cornersVec = output.mCCornersInfoVec.value;
                        console.log("    角点信息数量: " + cornersVec.length);

                        // 遍历所有角点信息
                        for (var i = 0; i < cornersVec.length; i++) {
                            var cornerInfo = cornersVec[i];
                            console.log("    === 角点组 " + i + " ===");

                            // 安全获取 corner 对象
                            var corner = null;
                            if (cornerInfo) {
                                if (cornerInfo.value) {
                                    corner = cornerInfo.value;
                                    console.log("    [GetCorners] 从 cornerInfo.value 获取");
                                } else {
                                    corner = cornerInfo;
                                    console.log("    [GetCorners] 从 cornerInfo 直接获取");
                                }
                            }

                            if (corner && corner.mCCornersInfoType) {
                                console.log("    类型: " + corner.mCCornersInfoType.value);

                                // 尝试获取坐标 - 增强版
                                try {
                                    var points = null;

                                    console.log("    [GetCorners DEBUG] corner 对象信息:");
                                    try {
                                        var fields = Object.getOwnPropertyNames(corner);
                                        for (var k = 0; k < Math.min(10, fields.length); k++) {
                                            console.log("      字段[" + k + "]: " + fields[k]);
                                        }
                                    } catch (e) {
                                        console.log("      无法获取字段列表: " + e);
                                    }

                                    // 方式1: 直接获取 mPoints
                                    try {
                                        if (corner.mPoints) {
                                            if (corner.mPoints.value) {
                                                points = corner.mPoints.value;
                                                console.log("    [GetCorners] 从 mPoints.value 获取坐标");
                                            } else {
                                                points = corner.mPoints;
                                                console.log("    [GetCorners] 从 mPoints 直接获取坐标");
                                            }
                                        }
                                    } catch (e) {
                                        console.log("    [GetCorners] mPoints 失败: " + e);
                                    }

                                    // 方式2: 尝试获取 getPointsRef()
                                    if (!points) {
                                        try {
                                            points = corner.getPointsRef();
                                            console.log("    [GetCorners] 从 getPointsRef() 获取坐标");
                                        } catch (e) {
                                            console.log("    [GetCorners] getPointsRef() 失败: " + e);
                                        }
                                    }

                                    // 方式3: 遍历查找坐标数组
                                    if (!points) {
                                        try {
                                            for (var prop in corner) {
                                                try {
                                                    var value = corner[prop];
                                                    if (value && value.length && value.length === 4) {
                                                        console.log("    [GetCorners] 发现可能的坐标数组字段: " + prop);
                                                        points = value;
                                                        break;
                                                    }
                                                } catch (e) {
                                                    // 忽略访问错误
                                                }
                                            }
                                        } catch (e) {
                                            console.log("    [GetCorners] 遍历字段失败: " + e);
                                        }
                                    }

                                    if (points && points.length > 0) {
                                        // 使用新的坐标比对函数
                                        compareCoordinates(points, "GetCorners 精确检测");
                                    } else {
                                        console.log("    [GetCorners] 未获取到有效坐标");
                                        console.log("    [GetCorners] corner 类型: " + typeof corner);
                                        console.log("    [GetCorners] corner 内容: " + JSON.stringify(corner, null, 2).substring(0, 300));
                                    }
                                } catch (e) {
                                    console.log("    [GetCorners] 获取坐标失败: " + e);
                                }
                            }
                        }
                    } else {
                        console.log("    角点信息: null 或未检测到");
                    }
                }
            } catch (e) {
                console.log("    获取输出参数失败: " + e);
            }

            return result;
        };

        console.log("[+] 成功钩住 CameraCleanAndroidShim");
    } catch (e) {
        console.log("[-] 钩住 CameraCleanAndroidShim 失败: " + e);
    }
}

// 钩住 CameraCleanLiveEdgeDetectionAndroidShim 类
function hookCameraCleanLiveEdgeDetectionAndroidShim() {
    try {
        var CameraCleanLiveEdgeDetectionAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanLiveEdgeDetectionAndroidShim");

        // 构造函数
        CameraCleanLiveEdgeDetectionAndroidShim.$init.overload('double').implementation = function(threshold) {
            console.log("[JAVA] 创建 CameraCleanLiveEdgeDetectionAndroidShim，阈值: " + threshold);
            return this.$init(threshold);
        };

        // getLiveCornersGray 方法
        if (CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray) {
            CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray.implementation = function(input, output) {
                console.log("[JAVA] CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray() 被调用");

                // 记录输入信息
                try {
                    if (input) {
                        console.log("    帧号: " + input.mFrameNumber.value);
                        console.log("    图像尺寸: " + input.mWidth.value + "x" + input.mHeight.value);
                    }
                } catch (e) {
                    console.log("    获取输入信息失败: " + e);
                }

                var result = this.getLiveCornersGray(input, output);

                // 记录输出信息
                try {
                    if (output) {
                        console.log("    检测结果: " + result);
                        var hintValue = output.mLiveBoundaryHint.value;
                        console.log("    边界提示: " + hintValue);

                        // 特别标记 "Looking for document" 状态
                        if (hintValue && hintValue.toString().indexOf("kLiveBoundaryHintLookingForDocument") !== -1) {
                            console.log("    [!] *** 正在显示 'Looking for document' 提示 ***");
                        }

                        // 检查角点信息 - 修复版
                        console.log("    [DEBUG] 检查 output 对象结构:");
                        try {
                            var outputFields = Object.getOwnPropertyNames(output);
                            for (var i = 0; i < Math.min(10, outputFields.length); i++) {
                                console.log("      output字段[" + i + "]: " + outputFields[i]);
                            }
                        } catch (e) {
                            console.log("      无法获取output字段列表: " + e);
                        }

                        // 尝试多种方式访问角点信息
                        var cornersInfo = null;

                        // 方式1: 检查 mCornersInfo
                        if (output.mCornersInfo) {
                            if (output.mCornersInfo.value) {
                                cornersInfo = output.mCornersInfo.value;
                                console.log("    [方式1] 从 mCornersInfo.value 获取角点信息");
                            } else {
                                cornersInfo = output.mCornersInfo;
                                console.log("    [方式1] 从 mCornersInfo 直接获取角点信息");
                            }
                        } else {
                            console.log("    [方式1] mCornersInfo 不存在");
                        }

                        // 方式2: 检查其他可能的字段
                        if (!cornersInfo) {
                            for (var prop in output) {
                                try {
                                    var value = output[prop];
                                    if (value && typeof value === 'object' && value.mCCornersInfoType) {
                                        cornersInfo = value;
                                        console.log("    [方式2] 从字段 " + prop + " 获取角点信息");
                                        break;
                                    }
                                } catch (e) {
                                    // 忽略访问错误
                                }
                            }
                        }

                        if (cornersInfo) {
                            console.log("    检测到角点");

                            // 尝试获取角点坐标 - 增强版 v3
                            try {
                                console.log("    角点信息类型: " + cornersInfo.mCCornersInfoType.value);

                                // 调试：打印 cornersInfo 的所有属性
                                console.log("    [DEBUG] cornersInfo 对象信息:");
                                try {
                                    var fields = Object.getOwnPropertyNames(cornersInfo);
                                    for (var i = 0; i < Math.min(10, fields.length); i++) {
                                        console.log("      字段[" + i + "]: " + fields[i]);
                                    }
                                } catch (e) {
                                    console.log("      无法获取字段列表: " + e);
                                }

                                // 尝试多种方式获取角点坐标
                                var points = null;

                                // 方式1: 直接获取 mPoints
                                try {
                                    if (cornersInfo.mPoints) {
                                        if (cornersInfo.mPoints.value) {
                                            points = cornersInfo.mPoints.value;
                                            console.log("    [方式1] 从 mPoints.value 获取到角点");
                                        } else {
                                            points = cornersInfo.mPoints;
                                            console.log("    [方式1] 从 mPoints 直接获取到角点");
                                        }
                                    }
                                } catch (e) {
                                    console.log("    [方式1] mPoints 失败: " + e);
                                }

                                // 方式2: 尝试获取 getPointsRef()
                                if (!points) {
                                    try {
                                        points = cornersInfo.getPointsRef();
                                        console.log("    [方式2] 从 getPointsRef() 获取到角点");
                                    } catch (e) {
                                        console.log("    [方式2] getPointsRef() 失败: " + e);
                                    }
                                }

                                // 方式3: 尝试其他可能的字段
                                if (!points) {
                                    try {
                                        if (cornersInfo.mCorners) {
                                            points = cornersInfo.mCorners.value || cornersInfo.mCorners;
                                            console.log("    [方式3] 从 mCorners 获取到角点");
                                        }
                                    } catch (e) {
                                        console.log("    [方式3] mCorners 失败: " + e);
                                    }
                                }

                                // 方式4: 尝试直接访问坐标数组
                                if (!points) {
                                    try {
                                        // 检查是否有数组类型的字段
                                        for (var prop in cornersInfo) {
                                            try {
                                                var value = cornersInfo[prop];
                                                if (value && value.length && value.length === 4) {
                                                    console.log("    [方式4] 发现可能的坐标数组字段: " + prop);
                                                    points = value;
                                                    break;
                                                }
                                            } catch (e) {
                                                // 忽略访问错误
                                            }
                                        }
                                    } catch (e) {
                                        console.log("    [方式4] 遍历字段失败: " + e);
                                    }
                                }

                                if (points && points.length > 0) {
                                    // 使用新的坐标比对函数
                                    compareCoordinates(points, "实时边缘检测");
                                } else {
                                    console.log("    未能获取到有效的角点坐标");
                                    console.log("    cornersInfo 类型: " + typeof cornersInfo);
                                    console.log("    cornersInfo 内容: " + JSON.stringify(cornersInfo, null, 2).substring(0, 500));
                                }
                            } catch (e) {
                                console.log("    获取角点坐标失败: " + e);
                                console.log("    错误堆栈: " + e.stack);
                            }
                        } else {
                            console.log("    未检测到角点信息");
                        }
                    }
                } catch (e) {
                    console.log("    获取输出信息失败: " + e);
                }

                return result;
            };
        }

        console.log("[+] 成功钩住 CameraCleanLiveEdgeDetectionAndroidShim");
    } catch (e) {
        console.log("[-] 钩住 CameraCleanLiveEdgeDetectionAndroidShim 失败: " + e);
    }
}

// 钩住 CameraCleanDocDetectionAndroidShim 类
function hookCameraCleanDocDetectionAndroidShim() {
    try {
        var CameraCleanDocDetectionAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanDocDetectionAndroidShim");

        // DetectDocType 方法
        CameraCleanDocDetectionAndroidShim.DetectDocType.implementation = function(input) {
            console.log("[JAVA] CameraCleanDocDetectionAndroidShim.DetectDocType() 被调用");

            try {
                if (input && input.mInputBitmap) {
                    var bitmap = input.mInputBitmap.value;
                    console.log("    输入图像尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    console.log("    是否为实时捕获: " + input.mIsLiveCaptureCase.value);
                }
            } catch (e) {
                console.log("    获取输入信息失败: " + e);
            }

            var result = this.DetectDocType(input);

            try {
                if (result) {
                    console.log("    检测结果: " + result.mDocClassification.value);
                    console.log("    置信度: " + result.mConfidence.value);
                }
            } catch (e) {
                console.log("    获取检测结果失败: " + e);
            }

            return result;
        };

        console.log("[+] 成功钩住 CameraCleanDocDetectionAndroidShim");
    } catch (e) {
        console.log("[-] 钩住 CameraCleanDocDetectionAndroidShim 失败: " + e);
    }
}

// 列出所有加载的模块
function listModules() {
    console.log("[+] 列出所有已加载的模块:");
    Process.enumerateModules().forEach(function(module) {
        console.log(module.name + " - " + module.base);
    });
}

// 搜索特定模块
function findModule(pattern) {
    console.log("[+] 搜索模块名包含 '" + pattern + "' 的模块:");
    var found = false;
    Process.enumerateModules().forEach(function(module) {
        if (module.name.toLowerCase().indexOf(pattern.toLowerCase()) !== -1) {
            console.log(module.name + " - " + module.base);
            found = true;
        }
    });
    if (!found) {
        console.log("[-] 未找到包含 '" + pattern + "' 的模块");
    }
}

// 查找特定模块的导出函数
function findExports(moduleName) {
    var module = Process.findModuleByName(moduleName);
    if (!module) {
        console.log("[-] 未找到模块: " + moduleName);
        return;
    }
    
    console.log("[+] 列出模块 " + moduleName + " 的导出函数:");
    var exports = module.enumerateExports();
    exports.forEach(function(exp) {
        console.log(exp.name + " - " + exp.address);
    });
}

// 查找模型文件
function findModelFiles() {
    console.log("[+] 尝试查找模型文件...");
    
    // 常见的模型文件路径
    var paths = [
        "/data/data/com.adobe.scan.android/files",
        "/data/data/com.adobe.scan.android/cache",
        "/data/data/com.adobe.scan.android/app_models",
        "/storage/emulated/0/Android/data/com.adobe.scan.android/files",
        "/storage/emulated/0/Android/data/com.adobe.scan.android/cache"
    ];
    
    paths.forEach(function(path) {
        try {
            console.log("[+] 检查路径: " + path);
            var files = Java.use("java.io.File").$new(path).listFiles();
            
            if (files) {
                console.log("[+] 找到 " + files.length + " 个文件/目录");
                
                for (var i = 0; i < files.length; i++) {
                    var file = files[i];
                    var fileName = file.getName();
                    var isDir = file.isDirectory();
                    var fileSize = file.length();
                    
                    console.log("    " + (isDir ? "[目录] " : "[文件] ") + fileName + (isDir ? "" : " (" + fileSize + " 字节)"));
                    
                    // 如果是目录，递归查找
                    if (isDir) {
                        var subFiles = file.listFiles();
                        if (subFiles) {
                            for (var j = 0; j < subFiles.length; j++) {
                                var subFile = subFiles[j];
                                var subFileName = subFile.getName();
                                var subIsDir = subFile.isDirectory();
                                var subFileSize = subFile.length();
                                
                                console.log("        " + (subIsDir ? "[目录] " : "[文件] ") + subFileName + (subIsDir ? "" : " (" + subFileSize + " 字节)"));
                                
                                // 检查是否为模型文件
                                if (!subIsDir && (subFileName.endsWith(".tflite") || 
                                                 subFileName.endsWith(".pb") || 
                                                 subFileName.endsWith(".onnx") || 
                                                 subFileName.endsWith(".model") || 
                                                 subFileName.endsWith(".bin"))) {
                                    console.log("[!] 可能的模型文件: " + path + "/" + fileName + "/" + subFileName);
                                }
                            }
                        }
                    }
                    
                    // 检查是否为模型文件
                    if (!isDir && (fileName.endsWith(".tflite") || 
                                  fileName.endsWith(".pb") || 
                                  fileName.endsWith(".onnx") || 
                                  fileName.endsWith(".model") || 
                                  fileName.endsWith(".bin"))) {
                        console.log("[!] 可能的模型文件: " + path + "/" + fileName);
                    }
                }
            } else {
                console.log("[-] 无法列出文件或目录为空");
            }
        } catch (e) {
            console.log("[-] 访问路径时出错: " + e);
        }
    });
}

// 钩住 UI 相关类
function hookUIClasses() {
    try {
        // 钩住 CaptureActivity
        var CaptureActivity = Java.use("com.adobe.dcmscan.CaptureActivity");

        // 钩住显示提示的方法
        if (CaptureActivity.updateLiveHint) {
            CaptureActivity.updateLiveHint.implementation = function(hint) {
                console.log("[UI] CaptureActivity.updateLiveHint() 被调用");
                console.log("    提示类型: " + hint);

                // 检查是否为 "Looking for document" 提示
                if (hint && hint.toString().indexOf("kLiveBoundaryHintLookingForDocument") !== -1) {
                    console.log("    [!] 显示 'Looking for document' 提示");
                }

                return this.updateLiveHint(hint);
            };
        }

        console.log("[+] 成功钩住 UI 相关类");
    } catch (e) {
        console.log("[-] 钩住 UI 相关类失败: " + e);
    }
}

// 钩住其他相关类
function hookOtherClasses() {
    try {
        // 钩住 LiveBoundaryHint 枚举的使用
        var LiveBoundaryHint = Java.use("com.adobe.magic_clean.CameraCleanLiveEdgeDetectionAndroidShim$LiveBoundaryHint");

        // 监控枚举值的创建和使用
        console.log("[+] 监控 LiveBoundaryHint 枚举");

        // 钩住 CCornersInfo 类
        var CCornersInfo = Java.use("com.adobe.magic_clean.CCornersInfo");

        // 钩住构造函数 - 处理多个重载
        CCornersInfo.$init.overload().implementation = function() {
            console.log("[JAVA] 创建 CCornersInfo 实例 (无参数)");
            return this.$init();
        };

        CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;').implementation = function(type, points) {
            console.log("[JAVA] 创建 CCornersInfo 实例 (类型: " + type + ", 点数: " + points.length + ")");

            // 直接在构造函数中捕获坐标！
            if (points && points.length > 0) {
                console.log("╔══════════════════════════════════════════════════════════════╗");
                console.log("║                    构造函数中的坐标数据                        ║");
                console.log("╠══════════════════════════════════════════════════════════════╣");
                for (var i = 0; i < points.length; i++) {
                    var point = points[i];
                    console.log("║ 点[" + i + "]: X=" + point.x + ", Y=" + point.y + "                           ║");
                }

                if (points.length === 4) {
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║                        文档四角坐标                           ║");
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 左上角: (" + points[0].x + ", " + points[0].y + ")");
                    console.log("║ 右上角: (" + points[1].x + ", " + points[1].y + ")");
                    console.log("║ 右下角: (" + points[2].x + ", " + points[2].y + ")");
                    console.log("║ 左下角: (" + points[3].x + ", " + points[3].y + ")");

                    // 计算文档尺寸
                    var width = Math.abs(points[1].x - points[0].x);
                    var height = Math.abs(points[3].y - points[0].y);
                    var diagonal = Math.sqrt(Math.pow(points[2].x - points[0].x, 2) + Math.pow(points[2].y - points[0].y, 2));

                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 宽度: " + width + " px");
                    console.log("║ 高度: " + height + " px");
                    console.log("║ 对角线: " + diagonal + " px");
                    console.log("║ 宽高比: " + (width/height));
                    console.log("║ 类型: " + type);
                }
                console.log("╚══════════════════════════════════════════════════════════════╝");
            }

            return this.$init(type, points);
        };

        CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;', 'boolean').implementation = function(type, points, flag) {
            console.log("[JAVA] 创建 CCornersInfo 实例 (类型: " + type + ", 点数: " + points.length + ", 标志: " + flag + ")");

            // 直接在构造函数中捕获坐标！
            if (points && points.length > 0) {
                console.log("╔══════════════════════════════════════════════════════════════╗");
                console.log("║                构造函数中的坐标数据 (带标志)                    ║");
                console.log("╠══════════════════════════════════════════════════════════════╣");
                for (var i = 0; i < points.length; i++) {
                    var point = points[i];
                    console.log("║ 点[" + i + "]: X=" + point.x + ", Y=" + point.y + "                           ║");
                }

                if (points.length === 4) {
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║                        文档四角坐标                           ║");
                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 左上角: (" + points[0].x + ", " + points[0].y + ")");
                    console.log("║ 右上角: (" + points[1].x + ", " + points[1].y + ")");
                    console.log("║ 右下角: (" + points[2].x + ", " + points[2].y + ")");
                    console.log("║ 左下角: (" + points[3].x + ", " + points[3].y + ")");

                    // 计算文档尺寸
                    var width = Math.abs(points[1].x - points[0].x);
                    var height = Math.abs(points[3].y - points[0].y);
                    var diagonal = Math.sqrt(Math.pow(points[2].x - points[0].x, 2) + Math.pow(points[2].y - points[0].y, 2));

                    console.log("╠══════════════════════════════════════════════════════════════╣");
                    console.log("║ 宽度: " + width + " px");
                    console.log("║ 高度: " + height + " px");
                    console.log("║ 对角线: " + diagonal + " px");
                    console.log("║ 宽高比: " + (width/height));
                    console.log("║ 类型: " + type + ", 标志: " + flag);
                }
                console.log("╚══════════════════════════════════════════════════════════════╝");
            }

            return this.$init(type, points, flag);
        };

        console.log("[+] 成功钩住其他相关类");
    } catch (e) {
        console.log("[-] 钩住其他相关类失败: " + e);
    }
}

// 增强的模块查找功能
function findModuleEnhanced(pattern) {
    console.log("[+] 增强搜索模块名包含 '" + pattern + "' 的模块:");
    var found = false;
    Process.enumerateModules().forEach(function(module) {
        if (module.name.toLowerCase().indexOf(pattern.toLowerCase()) !== -1) {
            console.log("    " + module.name + " - " + module.base + " (大小: " + module.size + ")");
            found = true;

            // 如果是 MagicClean 模块，显示更多信息
            if (module.name.toLowerCase().indexOf("magic") !== -1) {
                try {
                    var exports = module.enumerateExports();
                    console.log("      导出函数数量: " + exports.length);

                    var symbols = module.enumerateSymbols();
                    console.log("      符号数量: " + symbols.length);
                } catch (e) {
                    console.log("      获取模块信息失败: " + e);
                }
            }
        }
    });
    if (!found) {
        console.log("[-] 未找到包含 '" + pattern + "' 的模块");
    }
}

// 实时监控函数
function startLiveMonitoring() {
    console.log("[+] 开始实时监控边缘检测活动...");

    // 每5秒检查一次模块状态
    setInterval(function() {
        var magicCleanSo = Process.findModuleByName("libMagicClean.so");
        if (magicCleanSo && !hooksInstalled) {
            console.log("[+] 检测到 MagicClean 模块已加载，重新安装钩子");
            traceEdgeDetection();
        }
    }, 5000);
}

console.log("[+] 边缘检测跟踪脚本 v2.0 已加载");
console.log("[+] 可用命令:");
console.log("    traceEdgeDetection() - 开始跟踪");
console.log("    listModules() - 列出所有模块");
console.log("    findModule('pattern') - 搜索模块");
console.log("    findModuleEnhanced('pattern') - 增强搜索");
console.log("    findExports('moduleName') - 列出导出函数");
console.log("    findModelFiles() - 查找模型文件");
console.log("    startLiveMonitoring() - 开始实时监控");

// 坐标比对辅助函数
function compareCoordinates(points, source) {
    if (!points || points.length === 0) {
        return;
    }

    console.log("╔══════════════════════════════════════════════════════════════╗");
    console.log("║                    坐标信息 - " + source.padEnd(20) + "                    ║");
    console.log("╠══════════════════════════════════════════════════════════════╣");

    for (var i = 0; i < points.length; i++) {
        var point = points[i];
        var x = point.x.toFixed(2).padStart(8);
        var y = point.y.toFixed(2).padStart(8);
        console.log("║ 点[" + i + "]: X=" + x + ", Y=" + y + "                           ║");
    }

    if (points.length === 4) {
        console.log("╠══════════════════════════════════════════════════════════════╣");
        console.log("║                        文档四角标注                           ║");
        console.log("╠══════════════════════════════════════════════════════════════╣");
        console.log("║ 左上角: (" + points[0].x.toFixed(2) + ", " + points[0].y.toFixed(2) + ")");
        console.log("║ 右上角: (" + points[1].x.toFixed(2) + ", " + points[1].y.toFixed(2) + ")");
        console.log("║ 右下角: (" + points[2].x.toFixed(2) + ", " + points[2].y.toFixed(2) + ")");
        console.log("║ 左下角: (" + points[3].x.toFixed(2) + ", " + points[3].y.toFixed(2) + ")");

        // 计算边长和角度
        var width = Math.abs(points[1].x - points[0].x);
        var height = Math.abs(points[3].y - points[0].y);
        var diagonal = Math.sqrt(Math.pow(points[2].x - points[0].x, 2) + Math.pow(points[2].y - points[0].y, 2));

        console.log("╠══════════════════════════════════════════════════════════════╣");
        console.log("║ 宽度: " + width.toFixed(2) + " px");
        console.log("║ 高度: " + height.toFixed(2) + " px");
        console.log("║ 对角线: " + diagonal.toFixed(2) + " px");
        console.log("║ 宽高比: " + (width/height).toFixed(2));
    }

    console.log("╚══════════════════════════════════════════════════════════════╝");
}

// 添加屏幕坐标转换信息
function logScreenInfo() {
    console.log("📱 屏幕信息参考:");
    console.log("   - 相机预览尺寸: 640x480 (检测用)");
    console.log("   - 实际屏幕尺寸: 需要UI层确认");
    console.log("   - 坐标系原点: 左上角 (0,0)");
    console.log("   - X轴: 向右递增");
    console.log("   - Y轴: 向下递增");
    console.log("   ⚠️  注意: 检测坐标可能需要缩放到实际屏幕尺寸");
}

// 自动开始实时监控
startLiveMonitoring();

// 显示屏幕信息
logScreenInfo();
