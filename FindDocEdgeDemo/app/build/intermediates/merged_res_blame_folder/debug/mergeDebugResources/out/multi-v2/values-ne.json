{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,9675", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,9750"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3522,3625,3728,3830,3936,4034,4134,9993", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3620,3723,3825,3931,4029,4129,4237,10089"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3079,3168,3256,3338,3433,4242,4344,4454,4541,4601,4667,4763,4829,4890,4995,5059,5131,5189,5263,5325,5379,5492,5552,5613,5672,5750,5874,5955,6037,6137,6222,6307,6443,6524,6607,6738,6821,6907,6969,7023,7089,7166,7245,7316,7399,7468,7544,7625,7693,7797,7888,7966,8059,8156,8230,8309,8407,8467,8555,8621,8709,8797,8859,8927,8990,9056,9161,9267,9362,9467,9533,9591,9755,9844,9920", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3163,3251,3333,3428,3517,4339,4449,4536,4596,4662,4758,4824,4885,4990,5054,5126,5184,5258,5320,5374,5487,5547,5608,5667,5745,5869,5950,6032,6132,6217,6302,6438,6519,6602,6733,6816,6902,6964,7018,7084,7161,7240,7311,7394,7463,7539,7620,7688,7792,7883,7961,8054,8151,8225,8304,8402,8462,8550,8616,8704,8792,8854,8922,8985,9051,9156,9262,9357,9462,9528,9586,9670,9839,9915,9988"}}]}]}