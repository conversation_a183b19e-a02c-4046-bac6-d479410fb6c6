{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-or/values-or.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,326,433,519,623,743,822,903,994,1087,1188,1283,1383,1476,1571,1667,1758,1848,1937,2047,2151,2257,2368,2470,2588,2751,2857", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "211,321,428,514,618,738,817,898,989,1082,1183,1278,1378,1471,1566,1662,1753,1843,1932,2042,2146,2252,2363,2465,2583,2746,2852,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,433,543,650,736,840,960,1039,1120,1211,1304,1405,1500,1600,1693,1788,1884,1975,2065,2154,2264,2368,2474,2585,2687,2805,2968,9419", "endColumns": "110,109,106,85,103,119,78,80,90,92,100,94,99,92,94,95,90,89,88,109,103,105,110,101,117,162,105,89", "endOffsets": "428,538,645,731,835,955,1034,1115,1206,1299,1400,1495,1595,1688,1783,1879,1970,2060,2149,2259,2363,2469,2580,2682,2800,2963,3069,9504"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3481,3584,3686,3789,3894,3995,4097,9740", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3579,3681,3784,3889,3990,4092,4211,9836"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-or/values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1017,1082,1171,1236,1295,1381,1445,1509,1572,1645,1709,1763,1875,1933,1995,2049,2121,2243,2330,2406,2498,2580,2666,2806,2883,2964,3091,3182,3259,3313,3364,3430,3500,3577,3648,3723,3794,3871,3940,4009,4116,4207,4279,4368,4457,4531,4603,4689,4739,4818,4884,4964,5048,5110,5174,5237,5306,5406,5501,5593,5685,5743,5798,5882,5963,6038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "267,349,427,504,590,674,768,873,952,1012,1077,1166,1231,1290,1376,1440,1504,1567,1640,1704,1758,1870,1928,1990,2044,2116,2238,2325,2401,2493,2575,2661,2801,2878,2959,3086,3177,3254,3308,3359,3425,3495,3572,3643,3718,3789,3866,3935,4004,4111,4202,4274,4363,4452,4526,4598,4684,4734,4813,4879,4959,5043,5105,5169,5232,5301,5401,5496,5588,5680,5738,5793,5877,5958,6033,6108"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3074,3156,3234,3311,3397,4216,4310,4415,4494,4554,4619,4708,4773,4832,4918,4982,5046,5109,5182,5246,5300,5412,5470,5532,5586,5658,5780,5867,5943,6035,6117,6203,6343,6420,6501,6628,6719,6796,6850,6901,6967,7037,7114,7185,7260,7331,7408,7477,7546,7653,7744,7816,7905,7994,8068,8140,8226,8276,8355,8421,8501,8585,8647,8711,8774,8843,8943,9038,9130,9222,9280,9335,9509,9590,9665", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,81,77,76,85,83,93,104,78,59,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,75,91,81,85,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83,80,74,74", "endOffsets": "317,3151,3229,3306,3392,3476,4305,4410,4489,4549,4614,4703,4768,4827,4913,4977,5041,5104,5177,5241,5295,5407,5465,5527,5581,5653,5775,5862,5938,6030,6112,6198,6338,6415,6496,6623,6714,6791,6845,6896,6962,7032,7109,7180,7255,7326,7403,7472,7541,7648,7739,7811,7900,7989,8063,8135,8221,8271,8350,8416,8496,8580,8642,8706,8769,8838,8938,9033,9125,9217,9275,9330,9414,9585,9660,9735"}}]}]}