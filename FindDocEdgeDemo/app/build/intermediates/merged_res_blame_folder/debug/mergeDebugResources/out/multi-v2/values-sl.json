{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-sl/values-sl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,10035", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,10131"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,9710", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,9789"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,4651,4715,4781,4875,4951,5014,5126,5186,5251,5305,5375,5435,5491,5603,5660,5722,5778,5851,5985,6070,6147,6236,6317,6402,6545,6629,6712,6846,6935,7012,7068,7123,7189,7262,7339,7410,7489,7563,7639,7714,7787,7892,7980,8053,8143,8234,8306,8380,8471,8523,8605,8672,8756,8843,8905,8969,9032,9101,9204,9312,9410,9514,9574,9633,9794,9881,9957", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,4710,4776,4870,4946,5009,5121,5181,5246,5300,5370,5430,5486,5598,5655,5717,5773,5846,5980,6065,6142,6231,6312,6397,6540,6624,6707,6841,6930,7007,7063,7118,7184,7257,7334,7405,7484,7558,7634,7709,7782,7887,7975,8048,8138,8229,8301,8375,8466,8518,8600,8667,8751,8838,8900,8964,9027,9096,9199,9307,9405,9509,9569,9628,9705,9876,9952,10030"}}]}]}