{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,9625", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,9721"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,4397,4457,4522,4612,4679,4738,4828,4892,4956,5019,5088,5152,5206,5318,5376,5438,5492,5564,5686,5773,5848,5939,6020,6101,6241,6318,6399,6526,6617,6694,6748,6799,6865,6935,7012,7083,7158,7229,7306,7375,7444,7551,7642,7714,7803,7892,7966,8038,8124,8174,8253,8319,8399,8483,8545,8609,8672,8741,8841,8936,9028,9120,9178,9233,9394,9475,9550", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,4452,4517,4607,4674,4733,4823,4887,4951,5014,5083,5147,5201,5313,5371,5433,5487,5559,5681,5768,5843,5934,6015,6096,6236,6313,6394,6521,6612,6689,6743,6794,6860,6930,7007,7078,7153,7224,7301,7370,7439,7546,7637,7709,7798,7887,7961,8033,8119,8169,8248,8314,8394,8478,8540,8604,8667,8736,8836,8931,9023,9115,9173,9228,9306,9470,9545,9620"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,9311", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,9389"}}]}]}