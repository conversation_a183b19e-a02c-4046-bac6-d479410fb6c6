{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-as/values-as.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-as/values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,781,904,983,1043,1108,1197,1262,1321,1407,1471,1535,1598,1668,1732,1786,1891,1949,2011,2065,2137,2254,2341,2417,2509,2591,2674,2814,2891,2972,3099,3190,3267,3321,3372,3438,3508,3585,3656,3731,3802,3879,3948,4017,4124,4215,4287,4376,4465,4539,4611,4697,4747,4826,4892,4972,5056,5118,5182,5245,5314,5414,5509,5601,5693,5751,5806,5887,5968,6043", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "267,349,427,504,590,674,776,899,978,1038,1103,1192,1257,1316,1402,1466,1530,1593,1663,1727,1781,1886,1944,2006,2060,2132,2249,2336,2412,2504,2586,2669,2809,2886,2967,3094,3185,3262,3316,3367,3433,3503,3580,3651,3726,3797,3874,3943,4012,4119,4210,4282,4371,4460,4534,4606,4692,4742,4821,4887,4967,5051,5113,5177,5240,5309,5409,5504,5596,5688,5746,5801,5882,5963,6038,6113"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3065,3147,3225,3302,3388,4222,4324,4447,4526,4586,4651,4740,4805,4864,4950,5014,5078,5141,5211,5275,5329,5434,5492,5554,5608,5680,5797,5884,5960,6052,6134,6217,6357,6434,6515,6642,6733,6810,6864,6915,6981,7051,7128,7199,7274,7345,7422,7491,7560,7667,7758,7830,7919,8008,8082,8154,8240,8290,8369,8435,8515,8599,8661,8725,8788,8857,8957,9052,9144,9236,9294,9349,9513,9594,9669", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,81,77,76,85,83,101,122,78,59,64,88,64,58,85,63,63,62,69,63,53,104,57,61,53,71,116,86,75,91,81,82,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,80,80,74,74", "endOffsets": "317,3142,3220,3297,3383,3467,4319,4442,4521,4581,4646,4735,4800,4859,4945,5009,5073,5136,5206,5270,5324,5429,5487,5549,5603,5675,5792,5879,5955,6047,6129,6212,6352,6429,6510,6637,6728,6805,6859,6910,6976,7046,7123,7194,7269,7340,7417,7486,7555,7662,7753,7825,7914,8003,8077,8149,8235,8285,8364,8430,8510,8594,8656,8720,8783,8852,8952,9047,9139,9231,9289,9344,9425,9589,9664,9739"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3573,3676,3784,3889,3993,4093,9744", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3568,3671,3779,3884,3988,4088,4217,9840"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,430,529,636,727,832,952,1029,1104,1195,1288,1383,1477,1577,1670,1765,1859,1950,2041,2127,2240,2348,2451,2560,2676,2796,2963,9430", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "425,524,631,722,827,947,1024,1099,1190,1283,1378,1472,1572,1665,1760,1854,1945,2036,2122,2235,2343,2446,2555,2671,2791,2958,3060,9508"}}]}]}