{"logs": [{"outputFile": "com.example.finddocedgedemo.app-mergeDebugResources-31:/values-de/values-de.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,9975", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,10071"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,9657", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,9734"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/res/values-de/values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,4531,4593,4659,4753,4823,4882,4990,5056,5125,5183,5255,5319,5373,5501,5561,5623,5677,5755,5892,5984,6062,6156,6242,6326,6471,6555,6641,6774,6864,6943,7000,7051,7117,7191,7273,7344,7419,7493,7571,7643,7717,7827,7919,8001,8090,8179,8253,8331,8417,8472,8551,8618,8698,8782,8844,8908,8971,9040,9147,9254,9353,9459,9520,9575,9739,9822,9899", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,4588,4654,4748,4818,4877,4985,5051,5120,5178,5250,5314,5368,5496,5556,5618,5672,5750,5887,5979,6057,6151,6237,6321,6466,6550,6636,6769,6859,6938,6995,7046,7112,7186,7268,7339,7414,7488,7566,7638,7712,7822,7914,7996,8085,8174,8248,8326,8412,8467,8546,8613,8693,8777,8839,8903,8966,9035,9142,9249,9348,9454,9515,9570,9652,9817,9894,9970"}}]}]}