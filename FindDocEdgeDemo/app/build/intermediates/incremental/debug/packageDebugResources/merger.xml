<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res"><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-mdpi/ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-mdpi/ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-hdpi/ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-hdpi/ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/drawable/ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/drawable/ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xxxhdpi/ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="activity_main" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/layout/activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xxhdpi/ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/values-night/themes.xml" qualifiers="night-v8"><style name="Theme.FindDocEdgeDemo" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/values/colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/values/themes.xml" qualifiers=""><style name="Theme.FindDocEdgeDemo" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">FindDocEdgeDemo</string></file><file name="backup_rules" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xhdpi/ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-xhdpi/ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>