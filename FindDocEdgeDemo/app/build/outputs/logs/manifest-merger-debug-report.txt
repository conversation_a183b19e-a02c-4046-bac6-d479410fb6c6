-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:2:1-26:12
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:2:1-26:12
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:2:1-26:12
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:2:1-26:12
MERGED from [androidx.databinding:viewbinding:8.10.0-rc04] /Users/<USER>/.gradle/caches/8.11.1/transforms/283bfd0a5e045e86cd3c9df42306a76f/transformed/viewbinding-8.10.0-rc04/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/192c34f3d2ef44affb0ca4a61ae19b1c/transformed/constraintlayout-2.2.1/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87ecbecd520e8c18ea0917bcdac7118a/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3d6668474a219bc28f772025f5dfd483/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/83faff203f8d7b64183b655080b9100d/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/5558ce2f36e91e9b7469720d29f51dde/transformed/fragment-1.5.4/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/adf2ebee3e239535ab3d56aa64177a8f/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f1430daa216f8f0085fc45a88a354760/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3a529d87487c356632c95ff496041907/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f13db7f7e0745ddbb7bb4b1959d064a/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/923e9b2fc0443052689a40fa1b4ad1cf/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f2f97e869304165b58d5ca45ca2450ba/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87e6576f55eb3f92c7cb2f8206f5d0e1/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7b5ed43c8c194b350eca2cabc79dc0b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ec2c274e11a392182c68419f858aebe7/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6f8869d2a8f9e64026043ade93f97a2a/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0df6e30294078bd043657246b305a136/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/789e70c8cf4bf28226043fa755189f31/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a6082478ace3ad0ca74b48b09c9699c/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/8073e533a65757efe40d0cad4a088a3f/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/b30b147db4913c4546b153d07199bbed/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/a89e839c8a5b9cdddb8bc08cc461f2b5/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/291a5d1292de9c899bb515968abb4fd1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2b7c1413acfd3f7628ed9d52253db785/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/94c6f911a8301a3a15e73b11d267a4d6/transformed/core-ktx-1.16.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2854e216603b333cd9794f919b108d7/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b800f1f5db513f37c40b69a42393c74/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2111b4f8ddf96eba15910891112dd51/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84c4924012eef484f0dbcbc09d7c2393/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/8396bdf6d0299d56faefc16e1ff5b412/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d7d667f810389c64ac8537d73491d879/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/31f7cda4f572c7af5e589a3d2e0ba0da/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/497ad33f4f7c78761d9e80c2fc145a5a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/92557f214b1437a25e900a19dd3931f8/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/92ce1dc5ea33ed774ab087cde2802565/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/878b2cf6c2d83e59668112031018c082/transformed/core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:2:11-69
application
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:5:5-24:19
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:5:5-24:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/192c34f3d2ef44affb0ca4a61ae19b1c/transformed/constraintlayout-2.2.1/AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/192c34f3d2ef44affb0ca4a61ae19b1c/transformed/constraintlayout-2.2.1/AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/8396bdf6d0299d56faefc16e1ff5b412/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/8396bdf6d0299d56faefc16e1ff5b412/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:12:9-35
	android:label
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:14:9-29
	android:icon
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:6:9-35
	android:theme
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:13:9-53
	android:dataExtractionRules
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:7:9-65
activity#com.example.finddocedgedemo.MainActivity
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:15:9-23:20
	android:exported
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:17:13-36
	android:name
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:18:13-22:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:19:17-69
	android:name
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:19:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:21:17-77
	android:name
		ADDED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml:21:27-74
uses-sdk
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.0-rc04] /Users/<USER>/.gradle/caches/8.11.1/transforms/283bfd0a5e045e86cd3c9df42306a76f/transformed/viewbinding-8.10.0-rc04/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.0-rc04] /Users/<USER>/.gradle/caches/8.11.1/transforms/283bfd0a5e045e86cd3c9df42306a76f/transformed/viewbinding-8.10.0-rc04/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/de0ad383c008c08f84f62172c8535209/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/192c34f3d2ef44affb0ca4a61ae19b1c/transformed/constraintlayout-2.2.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/192c34f3d2ef44affb0ca4a61ae19b1c/transformed/constraintlayout-2.2.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87ecbecd520e8c18ea0917bcdac7118a/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87ecbecd520e8c18ea0917bcdac7118a/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/e92960a1ce27890536290413f67be3b3/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3d6668474a219bc28f772025f5dfd483/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3d6668474a219bc28f772025f5dfd483/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/83faff203f8d7b64183b655080b9100d/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/83faff203f8d7b64183b655080b9100d/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/5558ce2f36e91e9b7469720d29f51dde/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.11.1/transforms/5558ce2f36e91e9b7469720d29f51dde/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/adf2ebee3e239535ab3d56aa64177a8f/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/adf2ebee3e239535ab3d56aa64177a8f/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f1430daa216f8f0085fc45a88a354760/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f1430daa216f8f0085fc45a88a354760/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3a529d87487c356632c95ff496041907/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3a529d87487c356632c95ff496041907/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f13db7f7e0745ddbb7bb4b1959d064a/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/3f13db7f7e0745ddbb7bb4b1959d064a/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/923e9b2fc0443052689a40fa1b4ad1cf/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/923e9b2fc0443052689a40fa1b4ad1cf/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f2f97e869304165b58d5ca45ca2450ba/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/f2f97e869304165b58d5ca45ca2450ba/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87e6576f55eb3f92c7cb2f8206f5d0e1/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/87e6576f55eb3f92c7cb2f8206f5d0e1/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7b5ed43c8c194b350eca2cabc79dc0b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/7b5ed43c8c194b350eca2cabc79dc0b7/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ec2c274e11a392182c68419f858aebe7/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ec2c274e11a392182c68419f858aebe7/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6f8869d2a8f9e64026043ade93f97a2a/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6f8869d2a8f9e64026043ade93f97a2a/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0df6e30294078bd043657246b305a136/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0df6e30294078bd043657246b305a136/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/789e70c8cf4bf28226043fa755189f31/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/789e70c8cf4bf28226043fa755189f31/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a6082478ace3ad0ca74b48b09c9699c/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/6a6082478ace3ad0ca74b48b09c9699c/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/8073e533a65757efe40d0cad4a088a3f/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/8073e533a65757efe40d0cad4a088a3f/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/b30b147db4913c4546b153d07199bbed/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/b30b147db4913c4546b153d07199bbed/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/a89e839c8a5b9cdddb8bc08cc461f2b5/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/a89e839c8a5b9cdddb8bc08cc461f2b5/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/291a5d1292de9c899bb515968abb4fd1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/291a5d1292de9c899bb515968abb4fd1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2b7c1413acfd3f7628ed9d52253db785/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/2b7c1413acfd3f7628ed9d52253db785/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/94c6f911a8301a3a15e73b11d267a4d6/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/94c6f911a8301a3a15e73b11d267a4d6/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2854e216603b333cd9794f919b108d7/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/c2854e216603b333cd9794f919b108d7/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b800f1f5db513f37c40b69a42393c74/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1b800f1f5db513f37c40b69a42393c74/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2111b4f8ddf96eba15910891112dd51/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a2111b4f8ddf96eba15910891112dd51/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84c4924012eef484f0dbcbc09d7c2393/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/84c4924012eef484f0dbcbc09d7c2393/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/8396bdf6d0299d56faefc16e1ff5b412/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/8396bdf6d0299d56faefc16e1ff5b412/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d7d667f810389c64ac8537d73491d879/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d7d667f810389c64ac8537d73491d879/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/31f7cda4f572c7af5e589a3d2e0ba0da/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/31f7cda4f572c7af5e589a3d2e0ba0da/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/497ad33f4f7c78761d9e80c2fc145a5a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/497ad33f4f7c78761d9e80c2fc145a5a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/92557f214b1437a25e900a19dd3931f8/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/92557f214b1437a25e900a19dd3931f8/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/92ce1dc5ea33ed774ab087cde2802565/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/92ce1dc5ea33ed774ab087cde2802565/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/878b2cf6c2d83e59668112031018c082/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/878b2cf6c2d83e59668112031018c082/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/f6a664aa6f3b35e299903a4546a57143/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/770d17947daeaea8bfc4e9754f39b5f9/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
permission#com.example.finddocedgedemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
uses-permission#com.example.finddocedgedemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/76937e01dd53e2b36a099620e2f1ffb6/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.11.1/transforms/85a9fd7e80c19413d412e6534d3ecc48/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0f4602d310cf617d717a187c6e7e670b/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
