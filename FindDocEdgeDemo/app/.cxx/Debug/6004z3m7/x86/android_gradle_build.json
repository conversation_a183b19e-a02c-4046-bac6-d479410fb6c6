{"buildFiles": ["/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/cpp/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"finddocedgedemo::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "finddocedgedemo", "output": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/build/intermediates/cxx/Debug/6004z3m7/obj/x86/libfinddocedgedemo.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}