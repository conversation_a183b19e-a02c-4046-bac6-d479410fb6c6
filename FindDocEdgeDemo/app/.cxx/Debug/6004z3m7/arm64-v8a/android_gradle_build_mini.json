{"buildFiles": ["/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/cpp/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"finddocedgedemo::@6890427a1f51a3e7e1df": {"artifactName": "finddocedgedemo", "abi": "arm64-v8a", "output": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/build/intermediates/cxx/Debug/6004z3m7/obj/arm64-v8a/libfinddocedgedemo.so", "runtimeFiles": []}}}