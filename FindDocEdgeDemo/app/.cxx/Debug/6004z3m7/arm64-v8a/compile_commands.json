[{"directory": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dfinddocedgedemo_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles/finddocedgedemo.dir/native-lib.cpp.o -c \"/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/cpp/native-lib.cpp\"", "file": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/cpp/native-lib.cpp"}]