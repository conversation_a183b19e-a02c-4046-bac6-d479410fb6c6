{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "finddocedgedemo", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "finddocedgedemo::@6890427a1f51a3e7e1df", "jsonFile": "target-finddocedgedemo-Debug-7db2ee1ea591a912f6d3.json", "name": "finddocedgedemo", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/.cxx/Debug/6004z3m7/arm64-v8a", "source": "/Users/<USER>/Decompile_space/decompile_tools/Adobe Scan_25.02.12-google-dynamic/FindDocEdgeDemo/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}