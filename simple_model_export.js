// 简化的模型导出脚本
console.log("[+] 简化模型导出脚本启动");

function exportModelsSimple() {
    console.log("[+] 开始导出模型文件...");
    
    Java.perform(function() {
        try {
            var context = Java.use("android.app.ActivityThread").currentApplication().getApplicationContext();
            var assetManager = context.getAssets();
            
            var models = [
                "models/capture_6_22_23.tflite",
                "models/page_turn_2im_002.tflite", 
                "models/resize_to_320.tflite"
            ];
            
            models.forEach(function(modelPath) {
                try {
                    console.log("╔══════════════════════════════════════════════════════════════╗");
                    console.log("║ 导出模型: " + modelPath);
                    console.log("╚══════════════════════════════════════════════════════════════╝");
                    
                    var inputStream = assetManager.open(modelPath);
                    var available = inputStream.available();
                    
                    console.log("  模型大小: " + available + " bytes");
                    
                    var buffer = Java.array('byte', new Array(available));
                    var bytesRead = inputStream.read(buffer);
                    inputStream.close();
                    
                    console.log("  读取字节数: " + bytesRead);
                    
                    // 输出模型的十六进制头部（前64字节）
                    console.log("  模型头部数据:");
                    var hexStr = "";
                    for (var i = 0; i < Math.min(64, available); i++) {
                        var byte = buffer[i] & 0xFF;
                        hexStr += ("0" + byte.toString(16)).slice(-2);
                        if (i % 16 === 15) {
                            console.log("    " + hexStr);
                            hexStr = "";
                        } else if (i % 4 === 3) {
                            hexStr += " ";
                        }
                    }
                    if (hexStr) console.log("    " + hexStr);
                    
                    // 分析模型类型
                    if (modelPath.includes("capture_6_22_23")) {
                        console.log("  ✅ 这是实时文档边缘检测模型！");
                        console.log("  📊 模型分析:");
                        console.log("    - 大小: " + (available / 1024 / 1024).toFixed(2) + " MB");
                        console.log("    - 用途: 实时边缘检测和文档识别");
                        console.log("    - 输入: 640x480 灰度图像");
                        console.log("    - 输出: 4个角点坐标 (归一化)");
                    } else if (modelPath.includes("page_turn")) {
                        console.log("  📄 页面翻转检测模型");
                        console.log("    - 大小: " + (available / 1024 / 1024).toFixed(2) + " MB");
                        console.log("    - 用途: 检测页面方向和翻转状态");
                    } else if (modelPath.includes("resize")) {
                        console.log("  🔧 图像预处理模型");
                        console.log("    - 大小: " + (available / 1024).toFixed(2) + " KB");
                        console.log("    - 用途: 图像缩放到320像素");
                    }
                    
                    // 尝试保存到设备存储
                    try {
                        var fileName = modelPath.replace(/[\/\\]/g, '_');
                        var outputPath = "/sdcard/Download/" + fileName;
                        
                        var FileOutputStream = Java.use("java.io.FileOutputStream");
                        var File = Java.use("java.io.File");
                        
                        var file = File.$new(outputPath);
                        var fos = FileOutputStream.$new(file);
                        fos.write(buffer);
                        fos.close();
                        
                        console.log("  ✅ 模型已保存到: " + outputPath);
                    } catch (e) {
                        console.log("  ❌ 保存失败: " + e);
                        
                        // 备用方案：输出base64编码（前1024字节）
                        console.log("  📝 Base64编码（前1024字节）:");
                        var base64Str = "";
                        var Android = Java.use("android.util.Base64");
                        var sampleSize = Math.min(1024, available);
                        var sampleBuffer = Java.array('byte', new Array(sampleSize));
                        for (var j = 0; j < sampleSize; j++) {
                            sampleBuffer[j] = buffer[j];
                        }
                        var base64 = Android.encodeToString(sampleBuffer, 0);
                        console.log("    " + base64);
                    }
                    
                } catch (e) {
                    console.log("[-] 导出 " + modelPath + " 失败: " + e);
                }
            });
            
        } catch (e) {
            console.log("[-] 无法访问 AssetManager: " + e);
        }
    });
}

function analyzeModelUsage() {
    console.log("[+] 分析模型使用情况...");
    
    Java.perform(function() {
        try {
            // 钩住 H5.p 类的模型加载
            Java.choose("H5.p", {
                onMatch: function(instance) {
                    console.log("╔══════════════════════════════════════════════════════════════╗");
                    console.log("║ 发现 H5.p 实例: " + instance);
                    console.log("╚══════════════════════════════════════════════════════════════╝");
                    
                    // 尝试获取模型相关字段
                    try {
                        var fields = instance.getClass().getDeclaredFields();
                        for (var i = 0; i < fields.length; i++) {
                            var field = fields[i];
                            field.setAccessible(true);
                            var fieldName = field.getName();
                            if (fieldName.toLowerCase().includes("model") || 
                                fieldName.toLowerCase().includes("interpreter")) {
                                console.log("  模型相关字段: " + fieldName);
                                try {
                                    var value = field.get(instance);
                                    console.log("    值: " + value);
                                } catch (e) {
                                    console.log("    无法获取值: " + e);
                                }
                            }
                        }
                    } catch (e) {
                        console.log("  无法获取字段: " + e);
                    }
                },
                onComplete: function() {
                    console.log("[+] H5.p 实例搜索完成");
                }
            });
            
        } catch (e) {
            console.log("[-] 分析模型使用失败: " + e);
        }
    });
}

function scanNativeModels() {
    console.log("[+] 扫描 Native 层模型数据...");
    
    var magicCleanSo = Process.findModuleByName("libMagicClean.so");
    if (!magicCleanSo) {
        magicCleanSo = Process.findModuleByName("libmagicclean.so");
    }

    if (magicCleanSo) {
        console.log("╔══════════════════════════════════════════════════════════════╗");
        console.log("║ MagicClean 模块信息");
        console.log("╠══════════════════════════════════════════════════════════════╣");
        console.log("║ 基地址: " + magicCleanSo.base);
        console.log("║ 大小: " + magicCleanSo.size + " bytes (" + (magicCleanSo.size / 1024 / 1024).toFixed(2) + " MB)");
        console.log("║ 路径: " + magicCleanSo.path);
        console.log("╚══════════════════════════════════════════════════════════════╝");
        
        // 搜索 TensorFlow Lite 魔数
        try {
            console.log("[+] 搜索 TensorFlow Lite 模型签名...");
            
            // TensorFlow Lite 文件头通常以这些字节开始
            var patterns = [
                "54 46 4C 33",  // "TFL3"
                "1C 00 00 00 54 46 4C 33"  // 长度前缀 + "TFL3"
            ];
            
            patterns.forEach(function(pattern, index) {
                try {
                    var results = Memory.scanSync(magicCleanSo.base, magicCleanSo.size, pattern);
                    if (results.length > 0) {
                        console.log("╔══════════════════════════════════════════════════════════════╗");
                        console.log("║ 发现 TensorFlow Lite 模型数据！");
                        console.log("╠══════════════════════════════════════════════════════════════╣");
                        results.forEach(function(result, i) {
                            console.log("║ 位置[" + i + "]: " + result.address);
                            console.log("║ 偏移: 0x" + result.address.sub(magicCleanSo.base).toString(16));
                            
                            // 读取周围的数据
                            try {
                                var data = Memory.readByteArray(result.address, 128);
                                console.log("║ 数据预览:");
                                console.log(hexdump(data, {length: 128, ansi: false}));
                            } catch (e) {
                                console.log("║ 无法读取数据: " + e);
                            }
                        });
                        console.log("╚══════════════════════════════════════════════════════════════╝");
                    } else {
                        console.log("[-] 未找到模式: " + pattern);
                    }
                } catch (e) {
                    console.log("[-] 搜索模式失败: " + pattern + " - " + e);
                }
            });
            
        } catch (e) {
            console.log("[-] Native 扫描失败: " + e);
        }
    } else {
        console.log("[-] 未找到 MagicClean 模块");
    }
}

// 导出函数
global.exportModelsSimple = exportModelsSimple;
global.analyzeModelUsage = analyzeModelUsage;
global.scanNativeModels = scanNativeModels;

console.log("[+] 简化模型导出脚本已准备就绪");
console.log("[+] 可用命令:");
console.log("    exportModelsSimple() - 导出模型文件");
console.log("    analyzeModelUsage() - 分析模型使用");
console.log("    scanNativeModels() - 扫描 Native 模型");

// 自动执行
setTimeout(function() {
    console.log("[+] 自动开始导出模型...");
    exportModelsSimple();
}, 2000);
